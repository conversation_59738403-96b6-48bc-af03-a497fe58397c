<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100">
  <!-- Background circle with gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2a2a2a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1a1a1a;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e0e0e0;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Background circle -->
  <circle cx="50" cy="50" r="45" fill="url(#bgGradient)" stroke="#ffffff" stroke-width="1"/>

  <!-- Nerve Agent logo elements -->
  <g fill="url(#textGradient)">
    <!-- Stylized "N" with modern lines -->
    <path d="M20 30 L20 60 M20 30 L40 60 M40 30 L40 60" stroke="url(#textGradient)" stroke-width="3" fill="none" opacity="0.9"/>

    <!-- "A" text with modern font -->
    <text x="50" y="42" font-family="Arial, sans-serif" font-size="18" font-weight="bold">A</text>

    <!-- Decorative neural network dots -->
    <circle cx="25" cy="75" r="2" opacity="0.8"/>
    <circle cx="50" cy="80" r="2" opacity="0.8"/>
    <circle cx="75" cy="75" r="2" opacity="0.8"/>

    <!-- Connection lines -->
    <line x1="25" y1="75" x2="50" y2="80" stroke="url(#textGradient)" stroke-width="1" opacity="0.5"/>
    <line x1="50" y1="80" x2="75" y2="75" stroke="url(#textGradient)" stroke-width="1" opacity="0.5"/>
  </g>
</svg>
