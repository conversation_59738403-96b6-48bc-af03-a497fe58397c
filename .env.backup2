# Nerve Agent Local Development Configuration

# API Keys (Required for functionality)
API_KEY_OPENAI=your_openai_api_key_here
API_KEY_ANTHROPIC=
API_KEY_GROQ=
API_KEY_PERPLEXITY=
API_KEY_GOOGLE=
API_KEY_MISTRAL=

# Web UI Configuration
WEB_UI_PORT=8000
WEB_UI_HOST=127.0.0.1

# Authentication (Development credentials)
AUTH_LOGIN=admin
AUTH_PASSWORD=admin

# Security Settings
SECURE_HEADERS=false
SESSION_TIMEOUT=7200

# Development Settings
DEBUG=true
DEVELOPMENT_MODE=true
LOG_LEVEL=INFO

# Disable features that might cause issues in development
CODE_EXEC_DOCKER_ENABLED=false

# Local timezone
DEFAULT_USER_TIMEZONE=Africa/Johannesburg
