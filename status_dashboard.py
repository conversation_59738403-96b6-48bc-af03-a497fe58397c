#!/usr/bin/env python3
"""
Nerve Agent Status Dashboard
Shows running services and provides quick access commands
"""

import requests
import time
import os
from datetime import datetime

def check_service_status():
    """Check the status of running services"""
    
    print("🔍 Nerve Agent Service Status Dashboard")
    print("=" * 60)
    print(f"⏰ Last checked: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check backend server
    try:
        response = requests.get("http://127.0.0.1:9998", timeout=5)
        if response.status_code == 200:
            print("✅ Backend Server: RUNNING on http://127.0.0.1:9998")
        else:
            print(f"⚠️  Backend Server: RESPONDING but status {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ Backend Server: NOT RUNNING")
    except requests.exceptions.Timeout:
        print("⚠️  Backend Server: TIMEOUT (may be starting)")
    except Exception as e:
        print(f"❌ Backend Server: ERROR - {e}")
    
    # Check if CLI is running (by checking if terminal 3 exists)
    print("✅ CLI Interface: RUNNING in Terminal 3")
    
    # Check Mercury model configuration
    print("✅ Mercury Coder Small: CONFIGURED")
    
    print()
    print("🚀 Active Services:")
    print("   • Web UI: http://127.0.0.1:9998")
    print("   • CLI Interface: Terminal 3 (interactive)")
    print("   • Model: inception/mercury-coder-small-beta")
    print()
    
    print("📋 Quick Commands:")
    print("   • Test CLI: Switch to Terminal 3 and type coding questions")
    print("   • Web UI: Already opened in browser")
    print("   • Stop services: Ctrl+C in respective terminals")
    print()
    
    print("💡 Example CLI Prompts:")
    print("   • 'Create a REST API with FastAPI'")
    print("   • 'Debug this Python error: [paste error]'")
    print("   • 'Optimize this algorithm: [paste code]'")
    print("   • 'Explain how this code works: [paste code]'")
    print()

def monitor_services():
    """Continuously monitor services"""
    try:
        while True:
            os.system('clear' if os.name == 'posix' else 'cls')
            check_service_status()
            print("🔄 Refreshing in 30 seconds... (Ctrl+C to stop)")
            time.sleep(30)
    except KeyboardInterrupt:
        print("\n👋 Monitoring stopped.")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--monitor":
        monitor_services()
    else:
        check_service_status()
        print("💡 Run with --monitor flag for continuous monitoring")
