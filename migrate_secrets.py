#!/usr/bin/env python3
"""
Secret Migration Script for Nerve Agent

This script helps migrate API keys and other secrets from environment variables
to the encrypted secrets manager.
"""

import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from python.helpers.security.secrets_manager import get_secrets_manager
from python.helpers.dotenv import load_dotenv
from python.helpers.print_style import PrintStyle


def main():
    """Main migration function."""
    print("🔐 Nerve Agent Secrets Migration Tool")
    print("="*60)

    # Load environment variables
    load_dotenv()

    # Get secrets manager
    secrets_manager = get_secrets_manager()

    # Define API keys to migrate
    api_keys = [
        "API_KEY_OPENAI",
        "API_KEY_ANTHROPIC",
        "API_KEY_GROQ",
        "API_KEY_PERPLEXITY",
        "API_KEY_GOOGLE",
        "API_KEY_MISTRAL",
        "API_KEY_OPENROUTER",
        "API_KEY_SAMBANOVA",
        "API_KEY_OPENAI_AZURE",
        "OPENAI_AZURE_ENDPOINT",
        "OPENAI_API_VERSION",
        "HF_TOKEN",
        "HUGGINGFACEHUB_API_TOKEN",
        "CHUTES_API_TOKEN",
        "DEEPSEEK_API_KEY",
        "GROQ_API_KEY",
        "GOOGLE_API_KEY",
        "MISTRAL_API_KEY",
        "ANTHROPIC_API_KEY",
        "PERPLEXITY_API_KEY"
    ]

    # Check current secrets
    existing_secrets = secrets_manager.list_secrets()
    print(f"📋 Found {len(existing_secrets)} existing secrets in encrypted storage")

    if existing_secrets:
        print("Existing secrets:")
        for secret in existing_secrets:
            print(f"  - {secret}")

    print("\n🔍 Scanning environment variables...")

    # Find API keys in environment
    found_keys = []
    for key in api_keys:
        value = os.environ.get(key)
        if value and value.strip() and value != "None":
            found_keys.append((key, value))

    if not found_keys:
        print("✅ No API keys found in environment variables")
        print("💡 You can manually add secrets using the secrets manager")
        return

    print(f"\n📦 Found {len(found_keys)} API keys in environment:")
    for key, value in found_keys:
        masked_value = value[:8] + "..." + value[-4:] if len(value) > 12 else "***"
        print(f"  - {key}: {masked_value}")

    # Ask for confirmation
    print("\n⚠️  This will migrate the above API keys to encrypted storage.")
    print("The original environment variables will remain unchanged.")

    response = input("\nProceed with migration? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("❌ Migration cancelled")
        return

    # Migrate secrets
    print("\n🔄 Migrating secrets...")
    migrated_count = 0

    for key, value in found_keys:
        try:
            # Check if secret already exists
            try:
                existing_value = secrets_manager.get_secret(key, use_env_fallback=False)
                print(f"⚠️  Secret {key} already exists in encrypted storage, skipping")
                continue
            except:
                pass  # Secret doesn't exist, proceed with migration

            # Migrate the secret
            if secrets_manager.set_secret(key, value):
                migrated_count += 1
                PrintStyle(font_color="green").print(f"✅ Migrated: {key}")
            else:
                PrintStyle(font_color="red").print(f"❌ Failed to migrate: {key}")

        except Exception as e:
            PrintStyle(font_color="red").print(f"❌ Error migrating {key}: {str(e)}")

    print(f"\n📊 Migration Summary:")
    print(f"  - Total keys found: {len(found_keys)}")
    print(f"  - Successfully migrated: {migrated_count}")
    print(f"  - Failed/Skipped: {len(found_keys) - migrated_count}")

    if migrated_count > 0:
        print("\n🎉 Migration completed successfully!")
        print("\n💡 Next steps:")
        print("1. Test your application to ensure secrets are loaded correctly")
        print("2. Consider removing API keys from your .env file for better security")
        print("3. Set the NERVE_MASTER_KEY environment variable to persist encryption")

        # Show master key info
        master_key = os.environ.get("NERVE_MASTER_KEY")
        if not master_key:
            print("\n⚠️  IMPORTANT: No NERVE_MASTER_KEY found in environment!")
            print("   A temporary key was generated for this session.")
            print("   To persist your secrets, set NERVE_MASTER_KEY in your environment.")
            print("   You can generate a secure key with: python -c \"import secrets; print(secrets.token_urlsafe(32))\"")

    # Health check
    print("\n🏥 Running health check...")
    health = secrets_manager.health_check()

    print(f"  - Encryption initialized: {health['encryption_initialized']}")
    print(f"  - Secrets file exists: {health['secrets_file_exists']}")
    print(f"  - Secrets file readable: {health['secrets_file_readable']}")
    print(f"  - Total secrets count: {health['secrets_count']}")
    print(f"  - Cache size: {health['cache_size']}")

    if all([health['encryption_initialized'], health['secrets_file_readable']]):
        print("✅ Secrets manager is healthy!")
    else:
        print("❌ Secrets manager health check failed!")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ Migration cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Migration failed: {str(e)}")
        sys.exit(1)
