"""
Unit tests for the core Agent functionality.
"""

import asyncio
from unittest.mock import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest

from agent import Agent, AgentConfig, AgentContext
from python.helpers.tool import Response


@pytest.mark.unit
class TestAgent:
    """Test cases for the Agent class."""

    def test_agent_initialization(self, mock_agent_config):
        """Test agent initialization with valid config."""
        context = AgentContext(mock_agent_config)
        agent = Agent(0, mock_agent_config, context)

        assert agent.number == 0
        assert agent.agent_name == "Nerve"
        assert agent.config == mock_agent_config
        assert agent.context == context
        assert agent.history is not None
        assert agent.data == {}

    def test_agent_context_creation(self, mock_agent_config):
        """Test agent context creation and management."""
        context = AgentContext(mock_agent_config)

        assert context.config == mock_agent_config
        assert context.id is not None
        assert context.agent0 is not None
        assert context.paused is False
        assert context.created_at is not None

    @pytest.mark.asyncio
    async def test_agent_handle_intervention(self, mock_agent):
        """Test agent intervention handling."""
        # Test with no intervention
        await mock_agent.handle_intervention()

        # Test with intervention
        from agent import UserMessage

        intervention = UserMessage("test intervention", [])
        mock_agent.intervention = intervention

        # Should handle intervention without error
        await mock_agent.handle_intervention()

    def test_agent_parse_prompt(self, mock_agent, tmp_path):
        """Test prompt parsing functionality."""
        # Create a test prompt file
        prompt_dir = tmp_path / "prompts" / "default"
        prompt_dir.mkdir(parents=True)
        prompt_file = prompt_dir / "test.md"
        prompt_file.write_text("Test prompt content: {variable}")

        with patch("python.helpers.files.get_abs_path") as mock_get_path:
            mock_get_path.return_value = str(prompt_dir)

            with patch("python.helpers.files.parse_file") as mock_parse:
                mock_parse.return_value = "Parsed prompt content"

                result = mock_agent.parse_prompt("test.md", variable="test_value")
                assert result == "Parsed prompt content"
                mock_parse.assert_called_once()

    def test_agent_get_tool(self, mock_agent):
        """Test tool retrieval and instantiation."""
        with patch(
            "python.helpers.extract_tools.load_classes_from_folder"
        ) as mock_load:
            # Mock a tool class
            mock_tool_class = Mock()
            mock_tool_instance = Mock()
            mock_tool_class.return_value = mock_tool_instance
            mock_load.return_value = [mock_tool_class]

            tool = mock_agent.get_tool(
                name="test_tool",
                method="test_method",
                args={"arg1": "value1"},
                message="test message",
            )

            assert tool == mock_tool_instance
            mock_tool_class.assert_called_once_with(
                agent=mock_agent,
                name="test_tool",
                method="test_method",
                args={"arg1": "value1"},
                message="test message",
            )

    @pytest.mark.asyncio
    async def test_agent_rate_limiter(self, mock_agent):
        """Test rate limiter functionality."""
        with patch("python.helpers.rate_limiter.RateLimiter") as mock_limiter_class:
            mock_limiter = AsyncMock()
            mock_limiter_class.return_value = mock_limiter

            result = await mock_agent.rate_limiter(
                mock_agent.config.chat_model, "test prompt"
            )

            assert result == mock_limiter

    def test_agent_history_management(self, mock_agent):
        """Test agent history management."""
        # Test adding user message
        message = "Test user message"
        result = mock_agent.hist_add_user_message(message)

        assert result is not None

        # Test adding AI response
        response = "Test AI response"
        mock_agent.hist_add_ai_response(response)

        # Test adding tool result
        tool_name = "test_tool"
        tool_result = "Test tool result"
        mock_agent.hist_add_tool_result(tool_name, tool_result)

    def test_agent_data_management(self, mock_agent):
        """Test agent data storage and retrieval."""
        # Test setting data
        mock_agent.data["test_key"] = "test_value"
        assert mock_agent.data["test_key"] == "test_value"

        # Test getting data with default
        result = mock_agent.data.get("nonexistent_key", "default_value")
        assert result == "default_value"

    @pytest.mark.asyncio
    async def test_agent_exception_handling(self, mock_agent):
        """Test agent exception handling."""
        from python.helpers.errors import HandledException

        # Test handling of general exception
        test_exception = Exception("Test exception")

        with pytest.raises(HandledException):
            mock_agent.handle_critical_exception(test_exception)

    def test_agent_config_validation(self):
        """Test agent configuration validation."""
        from models import ModelConfig, ModelProvider

        # Test valid configuration
        config = AgentConfig(
            chat_model=ModelConfig(
                provider=ModelProvider.OPENAI, name="gpt-3.5-turbo", kwargs={}
            ),
            utility_model=ModelConfig(
                provider=ModelProvider.OPENAI, name="gpt-3.5-turbo", kwargs={}
            ),
            embeddings_model=ModelConfig(
                provider=ModelProvider.OPENAI, name="text-embedding-ada-002", kwargs={}
            ),
            browser_model=ModelConfig(
                provider=ModelProvider.OPENAI, name="gpt-3.5-turbo", kwargs={}
            ),
        )

        assert config.chat_model.provider == ModelProvider.OPENAI
        assert config.chat_model.name == "gpt-3.5-turbo"
        assert config.prompts_subdir == ""
        assert config.memory_subdir == ""
        assert config.code_exec_docker_enabled is False

    @pytest.mark.asyncio
    async def test_agent_message_processing(self, mock_agent):
        """Test agent message processing workflow."""
        from agent import UserMessage

        # Create a test user message
        test_message = UserMessage("Test message", [])

        # Test message processing
        mock_agent.last_user_message = test_message

        # Verify message is stored
        assert mock_agent.last_user_message.message == "Test message"
        assert mock_agent.last_user_message.attachments == []

    def test_agent_tool_loading(self, mock_agent):
        """Test agent tool loading and instantiation."""
        with patch(
            "python.helpers.extract_tools.load_classes_from_folder"
        ) as mock_load:
            # Mock multiple tool classes
            mock_tool1 = Mock()
            mock_tool2 = Mock()
            mock_tool_class1 = Mock(return_value=mock_tool1)
            mock_tool_class2 = Mock(return_value=mock_tool2)
            mock_load.return_value = [mock_tool_class1, mock_tool_class2]

            # Test tool loading
            tool = mock_agent.get_tool(
                name="test_tool",
                method="test_method",
                args={"arg1": "value1"},
                message="test message",
            )

            # Should return the first matching tool
            assert tool == mock_tool1
            mock_tool_class1.assert_called_once()

    def test_agent_data_persistence(self, mock_agent):
        """Test agent data storage and persistence."""
        # Test setting complex data structures
        test_data = {
            "string_value": "test",
            "number_value": 42,
            "list_value": [1, 2, 3],
            "dict_value": {"nested": "data"},
        }

        for key, value in test_data.items():
            mock_agent.data[key] = value

        # Verify all data is stored correctly
        for key, value in test_data.items():
            assert mock_agent.data[key] == value

        # Test data modification
        mock_agent.data["string_value"] = "modified"
        assert mock_agent.data["string_value"] == "modified"

    def test_agent_superior_subordinate_relationship(self, mock_agent_config):
        """Test agent superior/subordinate relationships."""
        # Create superior agent
        superior_context = AgentContext(mock_agent_config)
        superior_agent = Agent(0, mock_agent_config, superior_context)

        # Create subordinate agent
        subordinate_context = AgentContext(mock_agent_config)
        subordinate_agent = Agent(1, mock_agent_config, subordinate_context)

        # Set up relationship
        subordinate_agent.data[Agent.DATA_NAME_SUPERIOR] = superior_agent
        superior_agent.data[Agent.DATA_NAME_SUBORDINATE] = subordinate_agent

        # Verify relationship
        assert subordinate_agent.data[Agent.DATA_NAME_SUPERIOR] == superior_agent
        assert superior_agent.data[Agent.DATA_NAME_SUBORDINATE] == subordinate_agent


@pytest.mark.unit
class TestAgentContext:
    """Test cases for the AgentContext class."""

    def test_context_singleton_behavior(self, mock_agent_config):
        """Test context storage and retrieval."""
        context1 = AgentContext(mock_agent_config)
        context2 = AgentContext.get(context1.id)

        assert context2 == context1

    def test_context_removal(self, mock_agent_config):
        """Test context removal."""
        context = AgentContext(mock_agent_config)
        context_id = context.id

        removed_context = AgentContext.remove(context_id)
        assert removed_context == context

        # Should return None after removal
        assert AgentContext.get(context_id) is None

    def test_context_first(self, mock_agent_config):
        """Test getting first context."""
        # Clear existing contexts
        AgentContext._contexts.clear()

        # Should return None when no contexts
        assert AgentContext.first() is None

        # Create a context
        context = AgentContext(mock_agent_config)

        # Should return the first context
        first_context = AgentContext.first()
        assert first_context == context
