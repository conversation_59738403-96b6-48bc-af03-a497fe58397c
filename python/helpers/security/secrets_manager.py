"""
Secure secrets management for Nerve Agent.

This module provides encrypted storage and retrieval of sensitive data
like API keys, passwords, and other secrets.
"""

import base64
import json
import os
from typing import Any, Dict, List, Optional

from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from python.helpers.files import get_abs_path
from python.helpers.print_style import PrintStyle


class SecretNotFoundError(Exception):
    """Raised when a requested secret is not found."""

    pass


class SecretsManager:
    """
    Secure secrets manager with encryption.

    Stores secrets in an encrypted file using Fernet encryption.
    Supports environment variable fallback for backward compatibility.
    """

    def __init__(
        self, secrets_file: str = "secrets.enc", master_key: Optional[str] = None
    ):
        """
        Initialize the secrets manager.

        Args:
            secrets_file: Path to the encrypted secrets file
            master_key: Master key for encryption (if None, will be derived)
        """
        self.secrets_file = get_abs_path(secrets_file)
        self._master_key = master_key
        self._fernet = None
        self._secrets_cache: Dict[str, Any] = {}
        self._initialize_encryption()

    def _initialize_encryption(self):
        """Initialize encryption with master key."""
        if self._master_key is None:
            # Try to get master key from environment
            self._master_key = os.environ.get("NERVE_MASTER_KEY")

            if self._master_key is None:
                # Generate a new master key if none exists
                self._master_key = self._generate_master_key()
                PrintStyle(font_color="yellow", padding=True).print(
                    "Generated new master key. Set NERVE_MASTER_KEY environment variable to persist."
                )

        # Derive encryption key from master key
        key = self._derive_key(self._master_key)
        self._fernet = Fernet(key)

    def _generate_master_key(self) -> str:
        """Generate a new master key."""
        return base64.urlsafe_b64encode(os.urandom(32)).decode()

    def _derive_key(self, master_key: str) -> bytes:
        """Derive encryption key from master key."""
        # Use a fixed salt for key derivation (in production, use a random salt)
        salt = b"nerve_agent_salt_2024"
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(master_key.encode()))
        return key

    def _load_secrets(self) -> Dict[str, Any]:
        """Load and decrypt secrets from file."""
        if not os.path.exists(self.secrets_file):
            return {}

        try:
            with open(self.secrets_file, "rb") as f:
                encrypted_data = f.read()

            if not encrypted_data:
                return {}

            decrypted_data = self._fernet.decrypt(encrypted_data)
            return json.loads(decrypted_data.decode())

        except Exception as e:
            PrintStyle(font_color="red", padding=True).print(
                f"Error loading secrets: {str(e)}"
            )
            return {}

    def _save_secrets(self, secrets: Dict[str, Any]):
        """Encrypt and save secrets to file."""
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(self.secrets_file), exist_ok=True)

            # Encrypt and save
            data = json.dumps(secrets, indent=2).encode()
            encrypted_data = self._fernet.encrypt(data)

            with open(self.secrets_file, "wb") as f:
                f.write(encrypted_data)

            # Set restrictive permissions
            os.chmod(self.secrets_file, 0o600)

        except Exception as e:
            PrintStyle(font_color="red", padding=True).print(
                f"Error saving secrets: {str(e)}"
            )
            raise

    def get_secret(
        self, key: str, default: Any = None, use_env_fallback: bool = True
    ) -> Any:
        """
        Get a secret value.

        Args:
            key: Secret key
            default: Default value if secret not found
            use_env_fallback: Whether to check environment variables as fallback

        Returns:
            Secret value or default

        Raises:
            SecretNotFoundError: If secret not found and no default provided
        """
        # Check cache first
        if key in self._secrets_cache:
            return self._secrets_cache[key]

        # Load secrets from file
        secrets = self._load_secrets()

        if key in secrets:
            value = secrets[key]
            self._secrets_cache[key] = value
            return value

        # Fallback to environment variables if enabled
        if use_env_fallback:
            env_value = os.environ.get(key)
            if env_value is not None:
                return env_value

            # Try common API key patterns
            for pattern in [
                f"API_KEY_{key.upper()}",
                f"{key.upper()}_API_KEY",
                f"{key.upper()}_API_TOKEN",
            ]:
                env_value = os.environ.get(pattern)
                if env_value is not None:
                    return env_value

        # Return default or raise error
        if default is not None:
            return default

        raise SecretNotFoundError(f"Secret '{key}' not found")

    def set_secret(self, key: str, value: Any) -> bool:
        """
        Set a secret value.

        Args:
            key: Secret key
            value: Secret value

        Returns:
            True if successful
        """
        try:
            secrets = self._load_secrets()
            secrets[key] = value
            self._save_secrets(secrets)

            # Update cache
            self._secrets_cache[key] = value

            return True

        except Exception as e:
            PrintStyle(font_color="red", padding=True).print(
                f"Error setting secret '{key}': {str(e)}"
            )
            return False

    def delete_secret(self, key: str) -> bool:
        """
        Delete a secret.

        Args:
            key: Secret key to delete

        Returns:
            True if successful
        """
        try:
            secrets = self._load_secrets()

            if key in secrets:
                del secrets[key]
                self._save_secrets(secrets)

                # Remove from cache
                self._secrets_cache.pop(key, None)

                return True

            return False

        except Exception as e:
            PrintStyle(font_color="red", padding=True).print(
                f"Error deleting secret '{key}': {str(e)}"
            )
            return False

    def list_secrets(self) -> List[str]:
        """
        List all secret keys.

        Returns:
            List of secret keys
        """
        try:
            secrets = self._load_secrets()
            return list(secrets.keys())

        except Exception as e:
            PrintStyle(font_color="red", padding=True).print(
                f"Error listing secrets: {str(e)}"
            )
            return []

    def migrate_from_env(self, env_keys: List[str]) -> int:
        """
        Migrate secrets from environment variables to encrypted storage.

        Args:
            env_keys: List of environment variable keys to migrate

        Returns:
            Number of secrets migrated
        """
        migrated = 0

        for key in env_keys:
            value = os.environ.get(key)
            if value and value != "None":
                if self.set_secret(key, value):
                    migrated += 1
                    PrintStyle(font_color="green").print(f"Migrated secret: {key}")

        return migrated

    def clear_cache(self):
        """Clear the secrets cache."""
        self._secrets_cache.clear()

    def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check on the secrets manager.

        Returns:
            Health check results
        """
        results = {
            "encryption_initialized": self._fernet is not None,
            "secrets_file_exists": os.path.exists(self.secrets_file),
            "secrets_file_readable": False,
            "secrets_count": 0,
            "cache_size": len(self._secrets_cache),
        }

        if results["secrets_file_exists"]:
            try:
                secrets = self._load_secrets()
                results["secrets_file_readable"] = True
                results["secrets_count"] = len(secrets)
            except Exception:
                results["secrets_file_readable"] = False

        return results


# Global secrets manager instance
_secrets_manager: Optional[SecretsManager] = None


def get_secrets_manager() -> SecretsManager:
    """Get the global secrets manager instance."""
    global _secrets_manager
    if _secrets_manager is None:
        _secrets_manager = SecretsManager()
    return _secrets_manager


def get_secret(key: str, default: Any = None) -> Any:
    """Convenience function to get a secret."""
    return get_secrets_manager().get_secret(key, default)


def set_secret(key: str, value: Any) -> bool:
    """Convenience function to set a secret."""
    return get_secrets_manager().set_secret(key, value)
