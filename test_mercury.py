#!/usr/bin/env python3
"""
Test script for Mercury Coder Small integration
"""

import os
import sys
import json
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_mercury_integration():
    """Test Mercury Coder Small integration with Nerve Agent"""
    
    print("🧪 Testing Mercury Coder Small Integration")
    print("=" * 50)
    
    # Test 1: Check environment variables
    print("\n1. Checking Environment Variables...")
    openrouter_key = os.getenv("API_KEY_OPENROUTER")
    if openrouter_key and openrouter_key.startswith("sk-or-v1-"):
        print("   ✅ OpenRouter API key found and properly formatted")
    else:
        print("   ❌ OpenRouter API key missing or invalid")
        return False
    
    # Test 2: Check settings file
    print("\n2. Checking Settings Configuration...")
    settings_file = project_root / "tmp" / "settings.json"
    if settings_file.exists():
        with open(settings_file, 'r') as f:
            settings = json.load(f)
        
        if settings.get("chat_model_name") == "inception/mercury-coder-small-beta":
            print("   ✅ Mercury Coder Small configured as chat model")
        else:
            print(f"   ❌ Chat model is: {settings.get('chat_model_name')}")
            return False
            
        if settings.get("chat_model_provider") == "OPENROUTER":
            print("   ✅ OpenRouter configured as provider")
        else:
            print(f"   ❌ Provider is: {settings.get('chat_model_provider')}")
            return False
    else:
        print("   ❌ Settings file not found")
        return False
    
    # Test 3: Test model loading
    print("\n3. Testing Model Loading...")
    try:
        # Import the models module
        import models
        from models import ModelType, ModelProvider
        
        # Test creating Mercury model instance
        mercury_model = models.get_openrouter_chat(
            model_name="inception/mercury-coder-small-beta",
            api_key=openrouter_key,
            temperature=0.7
        )
        print("   ✅ Mercury model instance created successfully")
        
        # Test a simple completion
        print("\n4. Testing Model Response...")
        test_prompt = "Write a simple Python function that adds two numbers:"
        
        try:
            response = mercury_model.invoke(test_prompt)
            print("   ✅ Model responded successfully!")
            print(f"   📝 Response preview: {response.content[:100]}...")
            
            # Check if response contains code
            if "def " in response.content and "return" in response.content:
                print("   ✅ Response contains valid Python code")
            else:
                print("   ⚠️  Response may not contain expected code format")
                
        except Exception as e:
            print(f"   ❌ Model invocation failed: {e}")
            return False
            
    except ImportError as e:
        print(f"   ❌ Failed to import models: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Model loading failed: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 Mercury Coder Small Integration Test PASSED!")
    print("\n📊 Model Information:")
    print(f"   • Model: inception/mercury-coder-small-beta")
    print(f"   • Provider: OpenRouter")
    print(f"   • Context Length: 32,000 tokens")
    print(f"   • Pricing: $0.25/M input, $1/M output tokens")
    print(f"   • Speed: 5-10x faster than traditional LLMs")
    
    return True

def show_usage_examples():
    """Show examples of how to use Mercury Coder Small"""
    
    print("\n🚀 Usage Examples:")
    print("-" * 30)
    
    examples = [
        {
            "title": "Code Generation",
            "prompt": "Create a Python class for managing a todo list with add, remove, and list methods"
        },
        {
            "title": "Code Review",
            "prompt": "Review this Python code and suggest improvements: [paste your code here]"
        },
        {
            "title": "Debugging Help",
            "prompt": "Help me debug this error: [paste error message and code]"
        },
        {
            "title": "Code Explanation",
            "prompt": "Explain how this algorithm works: [paste algorithm code]"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['title']}:")
        print(f"   Prompt: \"{example['prompt']}\"")

if __name__ == "__main__":
    # Load environment variables
    from python.helpers.dotenv import load_dotenv
    load_dotenv()
    
    success = test_mercury_integration()
    
    if success:
        show_usage_examples()
        print("\n✨ Mercury Coder Small is ready to use!")
        print("   Start Nerve Agent with: python run_ui.py")
    else:
        print("\n❌ Integration test failed. Please check the configuration.")
        sys.exit(1)
