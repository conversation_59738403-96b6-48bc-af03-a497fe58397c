"""
Nerve Agent Agent Framework

This module provides the core agent implementation for the Nerve Agent framework,
including agent context management, hierarchical agent communication, and the main
agent execution loop with tool integration.

Key Components:
- AgentContext: Manages agent lifecycle, A2A communication, and context state
- Agent: Core agent class with message processing and tool execution
- AgentConfig: Configuration management for models and execution environment
- UserMessage: User input message structure with attachments and system messages

The framework supports:
- Hierarchical agent relationships (superior/subordinate)
- Agent-to-Agent (A2A) communication via Redis message bus
- Tool execution with intervention handling
- Memory management and conversation history
- Multi-model support (chat, utility, embeddings, browser)
- Docker and SSH-based code execution environments
"""

import asyncio
from collections import OrderedDict
from dataclasses import dataclass, field
from datetime import datetime
import json
from typing import Any, Awaitable, Coroutine, Optional, Dict, TypedDict
import uuid
import models

from python.helpers import extract_tools, rate_limiter, files, errors, history, tokens
from python.helpers import dirty_json
from python.helpers.print_style import PrintStyle
from langchain_core.prompts import (
    ChatPromptTemplate,
)
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage, BaseMessage

import python.helpers.log as Log
from python.helpers.dirty_json import DirtyJson
from python.helpers.defer import DeferredTask
from typing import Callable
from python.helpers.localization import Localization


class AgentContext:
    """
    Manages the execution context and lifecycle of agents in the Nerve Agent framework.

    AgentContext provides centralized management for agent instances, including:
    - Agent lifecycle management (creation, execution, termination)
    - A2A (Agent-to-Agent) communication setup and message handling
    - Task execution and intervention management
    - Logging and state persistence
    - Context serialization for UI and monitoring

    Attributes:
        _contexts: Global registry of all active agent contexts
        _counter: Global counter for assigning unique context numbers
        id: Unique identifier for this context
        name: Human-readable name for the context
        config: Agent configuration including model settings
        log: Logging instance for this context
        agent0: Primary agent instance (level 0 in hierarchy)
        paused: Whether the context is currently paused
        streaming_agent: Currently active agent for streaming responses
        task: Current deferred task being executed
        created_at: Timestamp when context was created
        no: Sequential number assigned to this context
        a2a_bus: A2A message bus instance for inter-agent communication
    """

    _contexts: dict[str, "AgentContext"] = {}
    _counter: int = 0

    def __init__(
        self,
        config: "AgentConfig",
        id: str | None = None,
        name: str | None = None,
        agent0: "Agent|None" = None,
        log: Log.Log | None = None,
        paused: bool = False,
        streaming_agent: "Agent|None" = None,
        created_at: datetime | None = None,
        enable_a2a: bool = False,
    ):
        """
        Initialize a new agent context.

        Args:
            config: Agent configuration including model settings
            id: Unique identifier (auto-generated if None)
            name: Human-readable name for the context
            agent0: Primary agent instance (created if None)
            log: Logging instance (created if None)
            paused: Whether to start in paused state
            streaming_agent: Currently active streaming agent
            created_at: Creation timestamp (current time if None)
            enable_a2a: Whether to enable A2A communication
        """
        # build context
        self.id = id or str(uuid.uuid4())
        self.name = name
        self.config = config
        self.log = log or Log.Log()
        self.agent0 = agent0 or Agent(0, self.config, self)
        self.paused = paused
        self.streaming_agent = streaming_agent
        self.task: DeferredTask | None = None
        self.created_at = created_at or datetime.now()
        AgentContext._counter += 1
        self.no = AgentContext._counter

        # Initialize A2A communication if enabled
        self.a2a_bus = None
        if enable_a2a:
            self._initialize_a2a()

        existing = self._contexts.get(self.id, None)
        if existing:
            AgentContext.remove(self.id)
        self._contexts[self.id] = self

    def _initialize_a2a(self):
        """
        Initialize A2A (Agent-to-Agent) communication for this agent context.

        Sets up Redis-based message bus for inter-agent communication.
        Registers message handler and logs initialization status.
        Gracefully handles Redis unavailability.
        """
        try:
            from python.tools.a2a import A2AMessageBus
            self.a2a_bus = A2AMessageBus()

            if self.a2a_bus.client:
                # Register message handler
                self.a2a_bus.subscribe(self._handle_a2a_message)
                self.log.log(
                    type="info",
                    content=f"A2A communication initialized for agent {self.id}"
                )
            else:
                self.log.log(
                    type="warning",
                    content="A2A communication failed to initialize - Redis not available"
                )
        except Exception as e:
            self.log.log(
                type="error",
                content=f"Failed to initialize A2A communication: {str(e)}"
            )
            self.a2a_bus = None

    def _handle_a2a_message(self, message: dict):
        """
        Handle incoming A2A messages for this agent context.

        Processes messages from other agents in the network and converts them
        to interventions for the current agent to handle.

        Args:
            message: A2A message dictionary containing type and payload
        """
        try:
            # Log the incoming message
            self.log.log(
                type="info",
                heading="A2A Message Received",
                content=str(message)
            )

            # If we have an active agent, let it handle the message
            if self.agent0:
                # Set the A2A message as an intervention
                from agent import UserMessage
                a2a_content = f"[A2A MESSAGE] {message.get('type', 'unknown')}: {message.get('payload', {})}"
                a2a_message = UserMessage(
                    message=a2a_content,
                    system_message=["Source: A2A Protocol"]
                )

                # Set intervention to process the A2A message
                if self.streaming_agent:
                    self.streaming_agent.intervention = a2a_message
                else:
                    self.agent0.intervention = a2a_message

        except Exception as e:
            self.log.log(
                type="error",
                content=f"Error handling A2A message: {str(e)}"
            )

    @staticmethod
    def get(id: str):
        return AgentContext._contexts.get(id, None)

    @staticmethod
    def first():
        if not AgentContext._contexts:
            return None
        return list(AgentContext._contexts.values())[0]

    @staticmethod
    def remove(id: str):
        context = AgentContext._contexts.pop(id, None)
        if context and context.task:
            context.task.kill()
        return context

    def serialize(self):
        return {
            "id": self.id,
            "name": self.name,
            "created_at": (
                Localization.get().serialize_datetime(self.created_at)
                if self.created_at else Localization.get().serialize_datetime(datetime.fromtimestamp(0))
            ),
            "no": self.no,
            "log_guid": self.log.guid,
            "log_version": len(self.log.updates),
            "log_length": len(self.log.logs),
            "paused": self.paused,
        }

    def get_created_at(self):
        return self.created_at

    def kill_process(self):
        if self.task:
            self.task.kill()

    def reset(self):
        self.kill_process()
        self.log.reset()
        self.agent0 = Agent(0, self.config, self)
        self.streaming_agent = None
        self.paused = False

    def nudge(self):
        self.kill_process()
        self.paused = False
        if self.streaming_agent:
            current_agent = self.streaming_agent
        else:
            current_agent = self.agent0

        self.task = self.run_task(current_agent.monologue)
        return self.task

    def communicate(self, msg: "UserMessage", broadcast_level: int = 1):
        self.paused = False  # unpause if paused

        if self.streaming_agent:
            current_agent = self.streaming_agent
        else:
            current_agent = self.agent0

        if self.task and self.task.is_alive():
            # set intervention messages to agent(s):
            intervention_agent = current_agent
            while intervention_agent and broadcast_level != 0:
                intervention_agent.intervention = msg
                broadcast_level -= 1
                intervention_agent = intervention_agent.data.get(
                    Agent.DATA_NAME_SUPERIOR, None
                )
        else:
            self.task = self.run_task(self._process_chain, current_agent, msg)

        return self.task

    def run_task(
        self, func: Callable[..., Coroutine[Any, Any, Any]], *args: Any, **kwargs: Any
    ):
        if not self.task:
            self.task = DeferredTask(
                thread_name=self.__class__.__name__,
            )
        self.task.start_task(func, *args, **kwargs)
        return self.task

    # this wrapper ensures that superior agents are called back if the chat was loaded from file and original callstack is gone
    async def _process_chain(self, agent: "Agent", msg: "UserMessage|str", user=True):
        try:
            msg_template = (
                agent.hist_add_user_message(msg)  # type: ignore
                if user
                else agent.hist_add_tool_result(
                    tool_name="call_subordinate", tool_result=msg  # type: ignore
                )
            )
            response = await agent.monologue()  # type: ignore
            superior = agent.data.get(Agent.DATA_NAME_SUPERIOR, None)
            if superior:
                response = await self._process_chain(superior, response, False)  # type: ignore
            return response
        except Exception as e:
            agent.handle_critical_exception(e)


@dataclass
class ModelConfig:
    provider: models.ModelProvider
    name: str
    ctx_length: int = 0
    limit_requests: int = 0
    limit_input: int = 0
    limit_output: int = 0
    vision: bool = False
    kwargs: dict = field(default_factory=dict)


@dataclass
class AgentConfig:
    chat_model: ModelConfig
    utility_model: ModelConfig
    embeddings_model: ModelConfig
    browser_model: ModelConfig
    prompts_subdir: str = ""
    memory_subdir: str = ""
    knowledge_subdirs: list[str] = field(default_factory=lambda: ["default", "custom"])
    code_exec_docker_enabled: bool = False
    code_exec_docker_name: str = "A0-dev"
    code_exec_docker_image: str = "frdel/nerve-agent-run:development"
    code_exec_docker_ports: dict[str, int] = field(
        default_factory=lambda: {"22/tcp": 55022, "80/tcp": 55080}
    )
    code_exec_docker_volumes: dict[str, dict[str, str]] = field(
        default_factory=lambda: {
            files.get_base_dir(): {"bind": "/a0", "mode": "rw"},
            files.get_abs_path("work_dir"): {"bind": "/root", "mode": "rw"},
        }
    )
    code_exec_ssh_enabled: bool = True
    code_exec_ssh_addr: str = "localhost"
    code_exec_ssh_port: int = 55022
    code_exec_ssh_user: str = "root"
    code_exec_ssh_pass: str = ""
    additional: Dict[str, Any] = field(default_factory=dict)


@dataclass
class UserMessage:
    message: str
    attachments: list[str] = field(default_factory=list[str])
    system_message: list[str] = field(default_factory=list[str])


class LoopData:
    def __init__(self, **kwargs):
        self.iteration = -1
        self.system = []
        self.user_message: history.Message | None = None
        self.history_output: list[history.OutputMessage] = []
        self.extras_temporary: OrderedDict[str, history.MessageContent] = OrderedDict()
        self.extras_persistent: OrderedDict[str, history.MessageContent] = OrderedDict()
        self.last_response = ""

        # override values with kwargs
        for key, value in kwargs.items():
            setattr(self, key, value)


# intervention exception class - skips rest of message loop iteration
class InterventionException(Exception):
    pass


# killer exception class - not forwarded to LLM, cannot be fixed on its own, ends message loop
class RepairableException(Exception):
    pass


class HandledException(Exception):
    pass


class Agent:

    DATA_NAME_SUPERIOR = "_superior"
    DATA_NAME_SUBORDINATE = "_subordinate"
    DATA_NAME_CTX_WINDOW = "ctx_window"

    def __init__(
        self, number: int, config: AgentConfig, context: AgentContext | None = None
    ):

        # agent config
        self.config = config

        # agent context
        self.context = context or AgentContext(config)

        # non-config vars
        self.number = number
        self.agent_name = "Nerve" if self.number == 0 else f"Agent {self.number}"

        self.history = history.History(self)
        self.last_user_message: history.Message | None = None
        self.intervention: UserMessage | None = None
        self.data = {}  # free data object all the tools can use

    async def monologue(self):
        """
        Main agent execution loop that handles conversation flow and tool execution.

        Manages the complete agent lifecycle including:
        - Extension callbacks for monologue phases
        - Message loop iteration with LLM interaction
        - Tool processing and response handling
        - Exception handling and recovery
        - Intervention processing

        Returns:
            Final response message when task is completed
        """
        while True:
            try:
                await self._initialize_monologue()
                result = await self._run_message_loop()
                if result:
                    return result
            except InterventionException:
                pass  # just start over
            except Exception as e:
                self.handle_critical_exception(e)
            finally:
                await self._cleanup_monologue()

    async def _initialize_monologue(self):
        """Initialize monologue loop data and call start extensions."""
        self.loop_data = LoopData(user_message=self.last_user_message)
        await self.call_extensions("monologue_start", loop_data=self.loop_data)

    async def _run_message_loop(self):
        """
        Run the main message processing loop.

        Returns:
            Final response message if task is completed, None otherwise
        """
        printer = PrintStyle(italic=True, font_color="#b3ffd9", padding=False)

        while True:
            self.context.streaming_agent = self  # mark self as current streamer
            self.loop_data.iteration += 1

            await self.call_extensions("message_loop_start", loop_data=self.loop_data)

            try:
                result = await self._process_single_iteration(printer)
                if result:
                    return result
            except InterventionException:
                pass  # intervention handled, continue loop
            except RepairableException as e:
                await self._handle_repairable_error(e)
            except Exception as e:
                self.handle_critical_exception(e)
            finally:
                await self.call_extensions("message_loop_end", loop_data=self.loop_data)

    async def _process_single_iteration(self, printer: PrintStyle):
        """
        Process a single iteration of the message loop.

        Args:
            printer: PrintStyle instance for output formatting

        Returns:
            Final response message if task is completed, None otherwise
        """
        # Prepare and execute LLM call
        prompt = await self.prepare_prompt(loop_data=self.loop_data)
        agent_response = await self._generate_agent_response(prompt, printer)

        await self.handle_intervention(agent_response)

        # Handle response processing
        if self.loop_data.last_response == agent_response:
            await self._handle_repeated_response(agent_response)
        else:
            return await self._handle_new_response(agent_response)

        return None

    async def _generate_agent_response(self, prompt: ChatPromptTemplate, printer: PrintStyle) -> str:
        """
        Generate agent response using the chat model.

        Args:
            prompt: Prepared chat prompt template
            printer: PrintStyle instance for output formatting

        Returns:
            Generated agent response text
        """
        try:
            # Output that the agent is starting
            PrintStyle(
                bold=True,
                font_color="green",
                padding=True,
                background_color="white",
            ).print(f"{self.agent_name}: Generating")

            log = self.context.log.log(
                type="agent", heading=f"{self.agent_name}: Generating"
            )

            async def stream_callback(chunk: str, full: str):
                if chunk:
                    printer.stream(chunk)
                    self.log_from_stream(full, log)

            return await self.call_chat_model(prompt, callback=stream_callback)

        except ValueError as e:
            if "API key" in str(e) or "model unavailable" in str(e):
                # Handle API key errors gracefully
                error_message = (
                    "⚠️ **Configuration Required**\n\n"
                    "I need an API key to respond to your message. Please:\n\n"
                    "1. Click the ⚙️ **Settings** icon in the top-right corner\n"
                    "2. Go to the **External Services** tab\n"
                    "3. Enter your API key for your preferred AI provider\n"
                    "4. Click **Save** to store your settings\n\n"
                    "**Supported Providers:**\n"
                    "- OpenAI (GPT-4, GPT-3.5)\n"
                    "- Anthropic (Claude)\n"
                    "- Groq (Fast inference)\n"
                    "- Google (Gemini)\n\n"
                    f"**Technical Details:** {str(e)}\n\n"
                    "Need help? Check the API Key Setup Guide in the documentation."
                )

                # Log the error
                self.context.log.log(
                    type="error",
                    heading="API Key Configuration Required",
                    content=str(e)
                )

                return error_message
            else:
                raise e

    async def _handle_repeated_response(self, agent_response: str):
        """Handle case where agent generates the same response as last time."""
        self.hist_add_ai_response(agent_response)
        warning_msg = self.read_prompt("fw.msg_repeat.md")
        self.hist_add_warning(message=warning_msg)
        PrintStyle(font_color="orange", padding=True).print(warning_msg)
        self.context.log.log(type="warning", content=warning_msg)

    async def _handle_new_response(self, agent_response: str):
        """
        Handle new agent response by processing tools.

        Args:
            agent_response: New agent response text

        Returns:
            Final response message if task is completed, None otherwise
        """
        self.hist_add_ai_response(agent_response)
        tools_result = await self.process_tools(agent_response)
        if tools_result:  # final response available
            return tools_result
        return None

    async def _handle_repairable_error(self, error: RepairableException):
        """Handle repairable errors by forwarding them to the LLM."""
        error_message = errors.format_error(error)
        self.hist_add_warning(error_message)
        PrintStyle(font_color="red", padding=True).print(error_message)
        self.context.log.log(type="error", content=error_message)

    async def _cleanup_monologue(self):
        """Clean up monologue state and call end extensions."""
        self.context.streaming_agent = None  # unset current streamer
        await self.call_extensions("monologue_end", loop_data=self.loop_data)

    async def prepare_prompt(self, loop_data: LoopData) -> ChatPromptTemplate:
        # call extensions before setting prompts
        await self.call_extensions("message_loop_prompts_before", loop_data=loop_data)

        # set system prompt and message history
        loop_data.system = await self.get_system_prompt(self.loop_data)
        loop_data.history_output = self.history.output()

        # and allow extensions to edit them
        await self.call_extensions("message_loop_prompts_after", loop_data=loop_data)

        # extras (memory etc.)
        # extras: list[history.OutputMessage] = []
        # for extra in loop_data.extras_persistent.values():
        #     extras += history.Message(False, content=extra).output()
        # for extra in loop_data.extras_temporary.values():
        #     extras += history.Message(False, content=extra).output()
        extras = history.Message(
            False,
            content=self.read_prompt("agent.context.extras.md", extras=dirty_json.stringify(
                {**loop_data.extras_persistent, **loop_data.extras_temporary}
                ))).output()
        loop_data.extras_temporary.clear()

        # convert history + extras to LLM format
        history_langchain: list[BaseMessage] = history.output_langchain(
            loop_data.history_output + extras
        )

        # build chain from system prompt, message history and model
        system_text = "\n\n".join(loop_data.system)
        prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessage(content=system_text),
                *history_langchain,
                # AIMessage(content="JSON:"), # force the LLM to start with json
            ]
        )

        # store as last context window content
        self.set_data(
            Agent.DATA_NAME_CTX_WINDOW,
            {
                "text": prompt.format(),
                "tokens": self.history.get_tokens()
                + tokens.approximate_tokens(system_text)
                + tokens.approximate_tokens(history.output_text(extras)),
            },
        )

        return prompt

    def handle_critical_exception(self, exception: Exception):
        if isinstance(exception, HandledException):
            raise exception  # Re-raise the exception to kill the loop
        elif isinstance(exception, asyncio.CancelledError):
            # Handling for asyncio.CancelledError
            PrintStyle(font_color="white", background_color="red", padding=True).print(
                f"Context {self.context.id} terminated during message loop"
            )
            raise HandledException(
                exception
            )  # Re-raise the exception to cancel the loop
        else:
            # Handling for general exceptions
            error_text = errors.error_text(exception)
            error_message = errors.format_error(exception)
            PrintStyle(font_color="red", padding=True).print(error_message)
            self.context.log.log(
                type="error",
                heading="Error",
                content=error_message,
                kvps={"text": error_text},
            )
            raise HandledException(exception)  # Re-raise the exception to kill the loop

    async def get_system_prompt(self, loop_data: LoopData) -> list[str]:
        system_prompt = []
        await self.call_extensions(
            "system_prompt", system_prompt=system_prompt, loop_data=loop_data
        )
        return system_prompt

    def parse_prompt(self, file: str, **kwargs):
        prompt_dir = files.get_abs_path("prompts/default")
        backup_dir = []
        if (
            self.config.prompts_subdir
        ):  # if agent has custom folder, use it and use default as backup
            prompt_dir = files.get_abs_path("prompts", self.config.prompts_subdir)
            backup_dir.append(files.get_abs_path("prompts/default"))
        prompt = files.parse_file(
            files.get_abs_path(prompt_dir, file), _backup_dirs=backup_dir, **kwargs
        )
        return prompt

    def read_prompt(self, file: str, **kwargs) -> str:
        prompt_dir = files.get_abs_path("prompts/default")
        backup_dir = []
        if (
            self.config.prompts_subdir
        ):  # if agent has custom folder, use it and use default as backup
            prompt_dir = files.get_abs_path("prompts", self.config.prompts_subdir)
            backup_dir.append(files.get_abs_path("prompts/default"))
        prompt = files.read_file(
            files.get_abs_path(prompt_dir, file), _backup_dirs=backup_dir, **kwargs
        )
        prompt = files.remove_code_fences(prompt)
        return prompt

    def get_data(self, field: str):
        return self.data.get(field, None)

    def set_data(self, field: str, value):
        self.data[field] = value

    def hist_add_message(
        self, ai: bool, content: history.MessageContent, tokens: int = 0
    ):
        return self.history.add_message(ai=ai, content=content, tokens=tokens)

    def hist_add_user_message(self, message: UserMessage, intervention: bool = False):
        self.history.new_topic()  # user message starts a new topic in history

        # load message template based on intervention
        if intervention:
            content = self.parse_prompt(
                "fw.intervention.md",
                message=message.message,
                attachments=message.attachments,
                system_message=message.system_message
            )
        else:
            content = self.parse_prompt(
                "fw.user_message.md",
                message=message.message,
                attachments=message.attachments,
                system_message=message.system_message
            )

        # remove empty parts from template
        if isinstance(content, dict):
            content = {k: v for k, v in content.items() if v}

        # add to history
        msg = self.hist_add_message(False, content=content)  # type: ignore
        self.last_user_message = msg
        return msg

    def hist_add_ai_response(self, message: str):
        self.loop_data.last_response = message
        content = self.parse_prompt("fw.ai_response.md", message=message)
        return self.hist_add_message(True, content=content)

    def hist_add_warning(self, message: history.MessageContent):
        content = self.parse_prompt("fw.warning.md", message=message)
        return self.hist_add_message(False, content=content)

    def hist_add_tool_result(self, tool_name: str, tool_result: str):
        content = self.parse_prompt(
            "fw.tool_result.md", tool_name=tool_name, tool_result=tool_result
        )
        return self.hist_add_message(False, content=content)

    def concat_messages(
        self, messages
    ):  # TODO add param for message range, topic, history
        return self.history.output_text(human_label="user", ai_label="assistant")

    def get_chat_model(self):
        try:
            return models.get_model(
                models.ModelType.CHAT,
                self.config.chat_model.provider,
                self.config.chat_model.name,
                **self.config.chat_model.kwargs,
            )
        except ValueError as e:
            if "API key" in str(e):
                # Log the API key error but don't crash
                self.context.log.log(
                    type="error",
                    heading="Chat Model Configuration Error",
                    content=str(e)
                )
                raise ValueError(f"Chat model unavailable: {str(e)}")
            else:
                raise e

    def get_utility_model(self):
        try:
            return models.get_model(
                models.ModelType.CHAT,
                self.config.utility_model.provider,
                self.config.utility_model.name,
                **self.config.utility_model.kwargs,
            )
        except ValueError as e:
            if "API key" in str(e):
                # Log the API key error but don't crash
                self.context.log.log(
                    type="error",
                    heading="Utility Model Configuration Error",
                    content=str(e)
                )
                raise ValueError(f"Utility model unavailable: {str(e)}")
            else:
                raise e

    def get_embedding_model(self):
        return models.get_model(
            models.ModelType.EMBEDDING,
            self.config.embeddings_model.provider,
            self.config.embeddings_model.name,
            **self.config.embeddings_model.kwargs,
        )

    async def call_utility_model(
        self,
        system: str,
        message: str,
        callback: Callable[[str], Awaitable[None]] | None = None,
        background: bool = False,
    ):
        prompt = ChatPromptTemplate.from_messages(
            [SystemMessage(content=system), HumanMessage(content=message)]
        )

        response = ""

        try:
            # model class
            model = self.get_utility_model()

            # rate limiter
            limiter = await self.rate_limiter(
                self.config.utility_model, prompt.format(), background
            )

            async for chunk in (prompt | model).astream({}):
                await self.handle_intervention()  # wait for intervention and handle it, if paused

                content = models.parse_chunk(chunk)
                limiter.add(output=tokens.approximate_tokens(content))
                response += content

                if callback:
                    await callback(content)

            return response

        except ValueError as e:
            if "API key" in str(e) or "model unavailable" in str(e):
                # For utility model, we can't return a user-facing message
                # Instead, we raise a more specific error that extensions can handle
                raise ValueError(f"Utility model unavailable: {str(e)}")
            else:
                raise e

    async def call_chat_model(
        self,
        prompt: ChatPromptTemplate,
        callback: Callable[[str, str], Awaitable[None]] | None = None,
    ):
        response = ""

        try:
            # model class
            model = self.get_chat_model()

            # rate limiter
            limiter = await self.rate_limiter(self.config.chat_model, prompt.format())

            async for chunk in (prompt | model).astream({}):
                await self.handle_intervention()  # wait for intervention and handle it, if paused

                content = models.parse_chunk(chunk)
                limiter.add(output=tokens.approximate_tokens(content))
                response += content

                if callback:
                    await callback(content, response)

            return response

        except ValueError as e:
            if "API key" in str(e) or "model unavailable" in str(e):
                # Return a helpful error message instead of crashing
                error_response = (
                    "⚠️ **Configuration Required**\n\n"
                    "I need an API key to respond to your message. Please:\n\n"
                    "1. Click the ⚙️ **Settings** icon in the top-right corner\n"
                    "2. Go to the **External Services** tab\n"
                    "3. Enter your API key for your preferred AI provider\n"
                    "4. Click **Save** to store your settings\n\n"
                    "**Supported Providers:**\n"
                    "- OpenAI (GPT-4, GPT-3.5)\n"
                    "- Anthropic (Claude)\n"
                    "- Groq (Fast inference)\n"
                    "- Google (Gemini)\n\n"
                    f"**Technical Details:** {str(e)}\n\n"
                    "Need help? Check the API Key Setup Guide in the documentation."
                )

                # Stream the error message if callback is provided
                if callback:
                    await callback(error_response, error_response)

                return error_response
            else:
                raise e

    async def rate_limiter(
        self, model_config: ModelConfig, input: str, background: bool = False
    ):
        # rate limiter log
        wait_log = None

        async def wait_callback(msg: str, key: str, total: int, limit: int):
            nonlocal wait_log
            if not wait_log:
                wait_log = self.context.log.log(
                    type="util",
                    update_progress="none",
                    heading=msg,
                    model=f"{model_config.provider.value}\\{model_config.name}",
                )
            wait_log.update(heading=msg, key=key, value=total, limit=limit)
            if not background:
                self.context.log.set_progress(msg, -1)

        # rate limiter
        limiter = models.get_rate_limiter(
            model_config.provider,
            model_config.name,
            model_config.limit_requests,
            model_config.limit_input,
            model_config.limit_output,
        )
        limiter.add(input=tokens.approximate_tokens(input))
        limiter.add(requests=1)
        await limiter.wait(callback=wait_callback)
        return limiter

    async def handle_intervention(self, progress: str = ""):
        while self.context.paused:
            await asyncio.sleep(0.1)  # wait if paused
        if (
            self.intervention
        ):  # if there is an intervention message, but not yet processed
            msg = self.intervention
            self.intervention = None  # reset the intervention message
            if progress.strip():
                self.hist_add_ai_response(progress)
            # append the intervention message
            self.hist_add_user_message(msg, intervention=True)
            raise InterventionException(msg)

    async def wait_if_paused(self):
        while self.context.paused:
            await asyncio.sleep(0.1)

    async def process_tools(self, msg: str):
        # search for tool usage requests in agent message
        tool_request = extract_tools.json_parse_dirty(msg)

        if tool_request is not None:
            tool_name = tool_request.get("tool_name", "")
            tool_method = None
            tool_args = tool_request.get("tool_args", {})

            if ":" in tool_name:
                tool_name, tool_method = tool_name.split(":", 1)

            tool = self.get_tool(name=tool_name, method=tool_method, args=tool_args, message=msg)

            await self.handle_intervention()  # wait if paused and handle intervention message if needed
            await tool.before_execution(**tool_args)
            await self.handle_intervention()  # wait if paused and handle intervention message if needed
            response = await tool.execute(**tool_args)
            await self.handle_intervention()  # wait if paused and handle intervention message if needed
            await tool.after_execution(response)
            await self.handle_intervention()  # wait if paused and handle intervention message if needed
            if response.break_loop:
                return response.message
        else:
            msg = self.read_prompt("fw.msg_misformat.md")
            self.hist_add_warning(msg)
            PrintStyle(font_color="red", padding=True).print(msg)
            self.context.log.log(
                type="error", content=f"{self.agent_name}: Message misformat"
            )

    def log_from_stream(self, stream: str, logItem: Log.LogItem):
        try:
            if len(stream) < 25:
                return  # no reason to try
            response = DirtyJson.parse_string(stream)
            if isinstance(response, dict):
                # log if result is a dictionary already
                logItem.update(content=stream, kvps=response)
        except Exception as e:
            pass

    def get_tool(self, name: str, method: str | None, args: dict, message: str, **kwargs):
        from python.tools.unknown import Unknown
        from python.helpers.tool import Tool

        classes = extract_tools.load_classes_from_folder(
            "python/tools", name + ".py", Tool
        )
        tool_class = classes[0] if classes else Unknown
        return tool_class(agent=self, name=name, method=method, args=args, message=message, **kwargs)

    async def call_extensions(self, folder: str, **kwargs) -> Any:
        from python.helpers.extension import Extension

        classes = extract_tools.load_classes_from_folder(
            "python/extensions/" + folder, "*", Extension
        )
        for cls in classes:
            await cls(agent=self).execute(**kwargs)
