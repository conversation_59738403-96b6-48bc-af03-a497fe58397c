{"chat_model_provider": "OPENROUTER", "chat_model_name": "inception/mercury-coder-small-beta", "chat_model_kwargs": {"temperature": "0.7"}, "chat_model_ctx_length": 32000, "chat_model_ctx_history": 1.0, "chat_model_vision": false, "chat_model_rl_requests": 0, "chat_model_rl_input": 0, "chat_model_rl_output": 0, "util_model_provider": "OPENROUTER", "util_model_name": "deepseek/deepseek-r1-0528-qwen3-8b:free", "util_model_ctx_length": 131072, "util_model_ctx_input": 0.7, "util_model_kwargs": {"temperature": "0"}, "util_model_rl_requests": 0, "util_model_rl_input": 0, "util_model_rl_output": 0, "embed_model_provider": "GOOGLE", "embed_model_name": "models/text-embedding-004", "embed_model_kwargs": {}, "embed_model_rl_requests": 0, "embed_model_rl_input": 0, "browser_model_provider": "OPENROUTER", "browser_model_name": "deepseek/deepseek-chat-v3-0324:free", "browser_model_vision": false, "browser_model_kwargs": {"temperature": "0.3"}, "api_keys": {}, "auth_login": "", "auth_password": "", "root_password": "", "agent_prompts_subdir": "default", "agent_memory_subdir": "default", "agent_knowledge_subdir": "custom", "rfc_auto_docker": true, "rfc_url": "localhost", "rfc_password": "", "rfc_port_http": 55080, "rfc_port_ssh": 55022, "stt_model_size": "base", "stt_language": "en", "stt_silence_threshold": 0.3, "stt_silence_duration": 1000, "stt_waiting_timeout": 2000}