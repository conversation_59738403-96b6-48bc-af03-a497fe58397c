#!/usr/bin/env python3
"""
Nerve Agent Python Launch Script
A cross-platform launcher for the Nerve Agent application.
"""

import os
import sys
import subprocess
import time
import signal
import argparse
import socket
from pathlib import Path

class Colors:
    """ANSI color codes for terminal output."""
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    NC = '\033[0m'  # No Color

class NerveLauncher:
    """Main launcher class for Nerve Agent."""

    def __init__(self):
        self.script_dir = Path(__file__).parent.absolute()
        self.log_dir = self.script_dir / "logs"
        self.pid_file = self.script_dir / "nerve.pid"
        self.default_port = 5001
        self.default_host = "localhost"

    def print_status(self, message):
        """Print status message in blue."""
        print(f"{Colors.BLUE}[INFO]{Colors.NC} {message}")

    def print_success(self, message):
        """Print success message in green."""
        print(f"{Colors.GREEN}[SUCCESS]{Colors.NC} {message}")

    def print_warning(self, message):
        """Print warning message in yellow."""
        print(f"{Colors.YELLOW}[WARNING]{Colors.NC} {message}")

    def print_error(self, message):
        """Print error message in red."""
        print(f"{Colors.RED}[ERROR]{Colors.NC} {message}")

    def check_port(self, port):
        """Check if a port is available."""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('127.0.0.1', port))
                return True
        except OSError:
            return False

    def find_available_port(self, start_port):
        """Find an available port starting from start_port."""
        port = start_port
        while not self.check_port(port):
            self.print_warning(f"Port {port} is in use, trying {port + 1}")
            port += 1
            if port > start_port + 100:
                self.print_error(f"Could not find available port after trying 100 ports starting from {start_port}")
                sys.exit(1)
        return port

    def check_dependencies(self):
        """Check if required dependencies are installed."""
        self.print_status("Checking dependencies...")

        required_packages = ["flask", "langchain_community", "faiss", "paramiko"]
        missing_packages = []

        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)

        if missing_packages:
            self.print_warning(f"Missing packages: {', '.join(missing_packages)}")
            self.print_warning("Some dependencies are missing. Please install them manually:")
            for package in missing_packages:
                self.print_status(f"  pip install {package}")
            self.print_warning("Or use conda/mamba if you're in a conda environment")
            self.print_status("Continuing anyway - the application may have limited functionality")

        self.print_success("Dependencies check completed")
        return True

    def setup_environment(self):
        """Setup the environment for running Nerve Agent."""
        self.print_status("Setting up environment...")

        # Create logs directory
        self.log_dir.mkdir(exist_ok=True)

        # Check for .env file
        env_file = self.script_dir / ".env"
        if not env_file.exists():
            self.print_warning(".env file not found")
            env_template = self.script_dir / ".env.local-docker"
            if env_template.exists():
                self.print_status("Copying .env.local-docker to .env")
                import shutil
                shutil.copy(env_template, env_file)
            else:
                self.print_warning("No environment template found. You may need to configure API keys manually.")

        self.print_success("Environment setup completed")

    def start_application(self, port=None, host=None):
        """Start the Nerve Agent application."""
        port = port or self.default_port
        host = host or self.default_host

        self.print_status("Starting Nerve Agent application...")

        # Find available port if specified port is in use
        if not self.check_port(port):
            if port == self.default_port:
                port = self.find_available_port(port)
                self.print_status(f"Using port {port}")
            else:
                self.print_error(f"Specified port {port} is already in use")
                return False

        # Change to script directory
        os.chdir(self.script_dir)

        self.print_status(f"Launching Nerve Agent on http://{host}:{port}")

        # Start the application
        log_file = self.log_dir / "nerve.log"
        cmd = [sys.executable, "run_ui.py", "--port", str(port), "--host", host]

        try:
            with open(log_file, "w") as f:
                process = subprocess.Popen(cmd, stdout=f, stderr=subprocess.STDOUT)

            # Save PID
            with open(self.pid_file, "w") as f:
                f.write(str(process.pid))

            # Wait a moment and check if process is still running
            time.sleep(3)
            if process.poll() is None:
                self.print_success("Nerve Agent started successfully!")
                self.print_success(f"PID: {process.pid}")
                self.print_success(f"Access the application at: http://{host}:{port}")
                self.print_status(f"Logs are available at: {log_file}")

                # Open browser if on macOS
                if sys.platform == "darwin":
                    self.print_status("Opening browser...")
                    subprocess.run(["open", f"http://{host}:{port}"])

                return True
            else:
                self.print_error("Failed to start Nerve Agent")
                self.print_error(f"Check logs at: {log_file}")
                return False

        except Exception as e:
            self.print_error(f"Failed to start application: {e}")
            return False

    def stop_application(self):
        """Stop the Nerve Agent application."""
        self.print_status("Stopping Nerve Agent...")

        if self.pid_file.exists():
            try:
                with open(self.pid_file, "r") as f:
                    pid = int(f.read().strip())

                try:
                    os.kill(pid, signal.SIGTERM)
                    self.print_success(f"Nerve Agent stopped (PID: {pid})")
                except ProcessLookupError:
                    self.print_warning(f"Process {pid} not found")

                self.pid_file.unlink()

            except (ValueError, FileNotFoundError):
                self.print_warning("Invalid or missing PID file")
        else:
            self.print_warning("PID file not found. Attempting to find and stop process...")
            # Try to find and stop any running instances
            try:
                result = subprocess.run(["pgrep", "-f", "python.*run_ui.py"],
                                      capture_output=True, text=True)
                if result.stdout.strip():
                    pids = result.stdout.strip().split('\n')
                    for pid in pids:
                        try:
                            os.kill(int(pid), signal.SIGTERM)
                        except (ValueError, ProcessLookupError):
                            pass
                    self.print_success(f"Stopped Nerve Agent processes: {', '.join(pids)}")
                else:
                    self.print_warning("No Nerve Agent processes found")
            except FileNotFoundError:
                self.print_warning("Could not search for processes (pgrep not available)")

    def show_status(self):
        """Show the status of the Nerve Agent application."""
        self.print_status("Checking Nerve Agent status...")

        if self.pid_file.exists():
            try:
                with open(self.pid_file, "r") as f:
                    pid = int(f.read().strip())

                try:
                    os.kill(pid, 0)  # Check if process exists
                    self.print_success(f"Nerve Agent is running (PID: {pid})")

                    # Try to determine the port
                    try:
                        result = subprocess.run(["lsof", "-p", str(pid), "-i", "TCP"],
                                              capture_output=True, text=True)
                        for line in result.stdout.split('\n'):
                            if 'LISTEN' in line:
                                port = line.split(':')[-1].split()[0]
                                self.print_status(f"Listening on port: {port}")
                                self.print_status(f"Access at: http://localhost:{port}")
                                break
                    except FileNotFoundError:
                        pass

                except ProcessLookupError:
                    self.print_warning(f"PID file exists but process {pid} is not running")
                    self.pid_file.unlink()

            except (ValueError, FileNotFoundError):
                self.print_warning("Invalid or missing PID file")
        else:
            try:
                result = subprocess.run(["pgrep", "-f", "python.*run_ui.py"],
                                      capture_output=True, text=True)
                if result.stdout.strip():
                    pids = result.stdout.strip().split('\n')
                    self.print_warning(f"Nerve Agent processes found but no PID file: {', '.join(pids)}")
                else:
                    self.print_status("Nerve Agent is not running")
            except FileNotFoundError:
                self.print_status("Nerve Agent is not running")

    def show_logs(self):
        """Show the application logs."""
        log_file = self.log_dir / "nerve.log"
        if log_file.exists():
            self.print_status("Showing recent logs...")
            try:
                subprocess.run(["tail", "-f", str(log_file)])
            except KeyboardInterrupt:
                pass
        else:
            self.print_warning(f"Log file not found at {log_file}")

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Nerve Agent Launch Script")
    parser.add_argument("command", nargs="?", default="start",
                       choices=["start", "stop", "restart", "status", "logs"],
                       help="Command to execute")
    parser.add_argument("--port", type=int, default=5001,
                       help="Port to run on (default: 5001)")
    parser.add_argument("--host", default="localhost",
                       help="Host to bind to (default: localhost)")

    args = parser.parse_args()
    launcher = NerveLauncher()

    if args.command == "start":
        if launcher.check_dependencies():
            launcher.setup_environment()
            launcher.start_application(args.port, args.host)
    elif args.command == "stop":
        launcher.stop_application()
    elif args.command == "restart":
        launcher.stop_application()
        time.sleep(2)
        if launcher.check_dependencies():
            launcher.setup_environment()
            launcher.start_application(args.port, args.host)
    elif args.command == "status":
        launcher.show_status()
    elif args.command == "logs":
        launcher.show_logs()

if __name__ == "__main__":
    main()
