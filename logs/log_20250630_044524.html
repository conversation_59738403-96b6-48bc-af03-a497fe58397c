<html><body style='background-color:black;font-family: Arial, Helvetica, sans-serif;'><pre>
<br><span style="color: rgb(255, 255, 0); ">Generated new master key. Set NERVE_MASTER_KEY environment variable to persist.</span><br>
<span style=" "><br><br>!!! run_cli.py is now discontinued. run_ui.py serves as both UI and API endpoint !!!<br><br></span><br>
<span style=" ">Initializing framework...</span><br>
<br><span style="font-weight: bold; color: rgb(255, 255, 255); background-color: rgb(108, 52, 131);">User message (&#x27;e&#x27; to leave):</span><br>
<span style="color: rgb(255, 255, 255); ">&gt; hello</span><br>
<br><span style="color: rgb(128, 128, 128); ">Debug: Changing timezone from None to Africa/Johannesburg</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: <PERSON>rror getting user API key for openrouter: &#x27;NoneType&#x27; object has no attribute &#x27;is_authenticated&#x27;</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for openrouter: &#x27;NoneType&#x27; object has no attribute &#x27;is_authenticated&#x27;</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for openrouter: &#x27;NoneType&#x27; object has no attribute &#x27;is_authenticated&#x27;</span><br>
<span style=" ">Initializing VectorDB...</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for google: &#x27;NoneType&#x27; object has no attribute &#x27;is_authenticated&#x27;</span><br>
<span style=" ">Found 2 knowledge files in /Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/knowledge/default/main, processing...</span><br>
<span style=" ">Processed 0 documents from 0 files.</span><br>
<span style=" ">Processed 0 documents from 0 files.</span><br>
<span style=" ">Processed 0 documents from 0 files.</span><br>
<span style=" ">Processed 0 documents from 0 files.</span><br>
<span style=" ">Processed 0 documents from 0 files.</span><br>
<span style=" ">Processed 0 documents from 0 files.</span><br>
<span style=" ">Processed 0 documents from 0 files.</span><br>
<span style=" ">Processed 0 documents from 0 files.</span><br>
<span style=" ">Found 1 knowledge files in /Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/instruments, processing...</span><br>
<span style=" ">Processed 0 documents from 0 files.</span><br>
<br><span style="font-weight: bold; color: rgb(0, 128, 0); background-color: rgb(255, 255, 255);">Nerve: Generating</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for openrouter: &#x27;NoneType&#x27; object has no attribute &#x27;is_authenticated&#x27;</span><br>
<span style="font-style: italic; color: rgb(179, 255, 217); ">{<br></span><span style="font-style: italic; color: rgb(179, 255, 217); ">    &quot;thoughts&quot;: [<br>        &quot;Hello! How can I assist you today?&quot;,<br>        &quot;</span><span style="font-style: italic; color: rgb(179, 255, 217); ">I am an autonomous cognitive AI agent that can create and deploy subordinate agents. These agents use the Model Context Protocol (MCP) to access external tools and data</span><span style="font-style: italic; color: rgb(179, 255, 217); ">, and they communicate with each other and with me using the Agent2Agent Protocol (A2A). I don&#x27;t have a specific name.&quot;<br>    ],<br>    &quot;tool_name&quot;: &quot;response</span><span style="font-style: italic; color: rgb(179, 255, 217); ">&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;text&quot;: &quot;Hello! How can I assist you today?&quot;<br>    }<br>}</span><br><br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for openrouter: &#x27;NoneType&#x27; object has no attribute &#x27;is_authenticated&#x27;</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Error getting user API key for openrouter: &#x27;NoneType&#x27; object has no attribute &#x27;is_authenticated&#x27;</span><br>
<br><span style="font-weight: bold; color: rgb(255, 255, 255); background-color: rgb(29, 131, 72);">Nerve: reponse:</span><br>
<span style="color: rgb(255, 255, 255); ">Hello! How can I assist you today?</span><br>
<br><span style="font-weight: bold; color: rgb(255, 255, 255); background-color: rgb(108, 52, 131);">User message (&#x27;e&#x27; to leave):</span><br>
