# Nerve Agent Local Docker Environment Configuration
# This file is optimized for running Nerve Agent in Docker on your local Mac

# API Keys (Using your existing configuration)
API_KEY_OPENAI=
API_KEY_ANTHROPIC=
API_KEY_GROQ=
API_KEY_PERPLEXITY=
API_KEY_GOOGLE=AIzaSyCq2VaZIccj4K8pSDtWWa-1gUZCuRut0wo
API_KEY_MISTRAL=
API_KEY_OPENROUTER=
API_KEY_SAMBANOVA=

# Azure OpenAI (if using)
API_KEY_OPENAI_AZURE=
OPENAI_AZURE_ENDPOINT=
OPENAI_API_VERSION=

# Hugging Face Token
HF_TOKEN=

# Web UI Configuration (Local Docker)
WEB_UI_PORT=5000
WEB_UI_HOST=0.0.0.0
USE_CLOUDFLARE=false

# Authentication (Local development credentials)
AUTH_LOGIN=admin
AUTH_PASSWORD=admin
RFC_PASSWORD=admin
ROOT_PASSWORD=admin
BASIC_AUTH_USERNAME=admin
BASIC_AUTH_PASSWORD=admin

# API Access
API_KEY=local-docker-api-key

# External Model Endpoints (Docker-compatible)
OLLAMA_BASE_URL=http://host.docker.internal:11434
LM_STUDIO_BASE_URL=http://host.docker.internal:1234/v1
OPEN_ROUTER_BASE_URL=https://openrouter.ai/api/v1
SAMBANOVA_BASE_URL=https://fast-api.snova.ai/v1

# Performance Settings
TOKENIZERS_PARALLELISM=true
PYDEVD_DISABLE_FILE_VALIDATION=1
KMP_DUPLICATE_LIB_OK=TRUE

# Local Docker Settings
DEFAULT_USER_TIMEZONE=Africa/Johannesburg
DOCKERIZED=true
CODE_EXEC_DOCKER_ENABLED=false

# Security Settings (Relaxed for local development)
SECURE_HEADERS=false
SESSION_TIMEOUT=7200

# Logging (Development-friendly)
LOG_LEVEL=DEBUG
LOG_FORMAT=text

# Local Development Features
DEBUG=true
DEVELOPMENT_MODE=true
