{"classifiers": ["Development Status :: 4 - Beta", "Environment :: Console", "Intended Audience :: <PERSON><PERSON><PERSON>", "License :: OSI Approved :: MIT License", "Operating System :: MacOS :: MacOS X", "Operating System :: Microsoft :: Windows", "Operating System :: POSIX :: Linux", "Programming Language :: Python :: 3 :: Only", "Programming Language :: Python :: 3.6"], "description_content_type": "UNKNOWN", "extensions": {"python.details": {"contacts": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>", "role": "author"}], "document_names": {"description": "DESCRIPTION.rst"}, "project_urls": {"Home": "http://github.com/johejo/inutimeout"}}}, "generator": "bdist_wheel (0.29.0)", "license": "MIT", "metadata_version": "2.0", "name": "inputimeout", "requires_python": ">=3.4", "summary": "Multi platform standard input with timeout", "test_requires": [{"requires": ["flake8", "pytest", "pytest-cov"]}], "version": "1.0.4"}