# Nerve Agent Production Environment Configuration
# Configure this file for your VPS deployment

# API Keys (Add your actual API keys here)
API_KEY_OPENAI=
API_KEY_ANTHROPIC=
API_KEY_GROQ=
API_KEY_PERPLEXITY=
API_KEY_GOOGLE=
API_KEY_MISTRAL=
API_KEY_OPENROUTER=
API_KEY_SAMBANOVA=

# Azure OpenAI (if using)
API_KEY_OPENAI_AZURE=
OPENAI_AZURE_ENDPOINT=
OPENAI_API_VERSION=

# Hugging Face Token
HF_TOKEN=

# Web UI Configuration
WEB_UI_PORT=5000
WEB_UI_HOST=0.0.0.0
USE_CLOUDFLARE=false

# Authentication (CHANGE THESE DEFAULT VALUES!)
AUTH_LOGIN=admin
AUTH_PASSWORD=changeme123!
RFC_PASSWORD=changeme123!
ROOT_PASSWORD=changeme123!
BASIC_AUTH_USERNAME=admin
BASIC_AUTH_PASSWORD=changeme123!

# API Access (Generate a secure API key)
API_KEY=your-secure-api-key-here

# External Model Endpoints
OLLAMA_BASE_URL=http://host.docker.internal:11434
LM_STUDIO_BASE_URL=http://host.docker.internal:1234/v1
OPEN_ROUTER_BASE_URL=https://openrouter.ai/api/v1
SAMBANOVA_BASE_URL=https://fast-api.snova.ai/v1

# Performance Settings
TOKENIZERS_PARALLELISM=true
PYDEVD_DISABLE_FILE_VALIDATION=1

# Production Settings
DEFAULT_USER_TIMEZONE=UTC
DOCKERIZED=true
CODE_EXEC_DOCKER_ENABLED=false

# Security Settings
SECURE_HEADERS=true
SESSION_TIMEOUT=3600

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json
